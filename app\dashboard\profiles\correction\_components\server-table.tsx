import { ExpressionTest, ExpressionTestResult } from '@/types';
import ExpressionList from './list';
import { Skeleton } from '@/components/ui/skeleton';
import axios from 'axios';
import { getAuthSession } from '@/lib/auth';
import logger from '@/lib/logger';

// Types pour la gestion d'erreurs
type ApiResponse<T> =
  | {
      success: true;
      data: T;
    }
  | {
      success: false;
      error: string;
    };

const baseUrl =
  process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || '';

async function getTestsEE(
  token: string,
): Promise<ApiResponse<ExpressionTestResult[]>> {
  try {
    if (!token) {
      return { success: false, error: "Token d'authentification manquant" };
    }

    const { data } = await axios.get<{ tests: ExpressionTest[] }>(
      `${baseUrl}/api/dealer/profiles/testsee-en-cours`,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      },
    );

    if (!data || !Array.isArray(data.tests)) {
      return {
        success: false,
        error: 'Format de données invalide reçu du serveur',
      };
    }

    const testsWithType = data.tests.map((test) => ({
      ...test,
      type: 'EE' as const,
    }));
    return { success: true, data: testsWithType };
  } catch (error) {
    logger.error('Erreur lors de la récupération des tests EE:', error);
    const errorMessage = axios.isAxiosError(error)
      ? `Erreur API: ${error.response?.status} - ${error.response?.statusText || error.message}`
      : 'Erreur inconnue lors de la récupération des tests EE';
    return { success: false, error: errorMessage };
  }
}

async function getTestsEO(
  token: string,
): Promise<ApiResponse<ExpressionTestResult[]>> {
  try {
    if (!token) {
      return { success: false, error: "Token d'authentification manquant" };
    }

    const { data } = await axios.get<{ tests: ExpressionTest[] }>(
      `${baseUrl}/api/dealer/profiles/testseo-en-cours`,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      },
    );

    if (!data || !Array.isArray(data.tests)) {
      return {
        success: false,
        error: 'Format de données invalide reçu du serveur',
      };
    }

    const testsWithType = data.tests.map((test) => ({
      ...test,
      type: 'EO' as const,
    }));
    return { success: true, data: testsWithType };
  } catch (error) {
    logger.error('Erreur lors de la récupération des tests EO:', error);
    const errorMessage = axios.isAxiosError(error)
      ? `Erreur API: ${error.response?.status} - ${error.response?.statusText || error.message}`
      : 'Erreur inconnue lors de la récupération des tests EO';
    return { success: false, error: errorMessage };
  }
}

async function getTestsEESuperdealer(
  token: string,
  groupId?: string,
): Promise<ApiResponse<ExpressionTestResult[]>> {
  try {
    if (!groupId) {
      return { success: true, data: [] };
    }

    if (!token) {
      return { success: false, error: "Token d'authentification manquant" };
    }

    const { data } = await axios.get<{ tests: ExpressionTest[] }>(
      `${baseUrl}/api/eeTest/tests/pending/by-group/${groupId}`,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      },
    );

    if (!data || !Array.isArray(data.tests)) {
      return {
        success: false,
        error: 'Format de données invalide reçu du serveur',
      };
    }

    const testsWithType = data.tests.map((test) => ({
      ...test,
      type: 'EE' as const,
    }));
    return { success: true, data: testsWithType };
  } catch (error) {
    logger.error(
      'Erreur lors de la récupération des tests EE Superdealer:',
      error,
    );
    const errorMessage = axios.isAxiosError(error)
      ? `Erreur API: ${error.response?.status} - ${error.response?.statusText || error.message}`
      : 'Erreur inconnue lors de la récupération des tests EE Superdealer';
    return { success: false, error: errorMessage };
  }
}

async function getTestsEOSuperdealer(
  token: string,
  groupId?: string,
): Promise<ApiResponse<ExpressionTestResult[]>> {
  try {
    if (!groupId) {
      return { success: true, data: [] };
    }

    if (!token) {
      return { success: false, error: "Token d'authentification manquant" };
    }

    const { data } = await axios.get<{ tests: ExpressionTest[] }>(
      `${baseUrl}/api/eoTest/tests/pending/by-group/${groupId}`,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      },
    );

    if (!data || !Array.isArray(data.tests)) {
      return {
        success: false,
        error: 'Format de données invalide reçu du serveur',
      };
    }

    const testsWithType = data.tests.map((test) => ({
      ...test,
      type: 'EO' as const,
    }));
    return { success: true, data: testsWithType };
  } catch (error) {
    logger.error(
      'Erreur lors de la récupération des tests EO Superdealer:',
      error,
    );
    const errorMessage = axios.isAxiosError(error)
      ? `Erreur API: ${error.response?.status} - ${error.response?.statusText || error.message}`
      : 'Erreur inconnue lors de la récupération des tests EO Superdealer';
    return { success: false, error: errorMessage };
  }
}

// Composant d'affichage d'erreur
const ErrorDisplay = ({ error }: { error: string }) => {
  return (
    <div className="flex items-center justify-center rounded-md border border-red-200 bg-red-50 p-8">
      <div className="text-center">
        <div className="mb-2 font-medium text-red-600">
          Erreur lors du chargement des données
        </div>
        <div className="text-sm text-red-500">{error}</div>
      </div>
    </div>
  );
};

export const WritingExpression = async ({
  groupId,
}: {
  groupId: string | undefined;
}) => {
  try {
    const session = await getAuthSession();

    if (!session?.user) {
      return <ErrorDisplay error="Session utilisateur non trouvée" />;
    }

    const token = session.user.accessToken || '';
    const response =
      session.user.role === 'dealer'
        ? await getTestsEE(token)
        : await getTestsEESuperdealer(token, groupId);

    if (!response.success) {
      return <ErrorDisplay error={response.error} />;
    }

    return <ExpressionList data={response.data} />;
  } catch (error) {
    logger.error('Erreur dans WritingExpression:', error);
    return (
      <ErrorDisplay error="Erreur inattendue lors du chargement des tests d'expression écrite" />
    );
  }
};

export const OralExpression = async ({
  groupId,
}: {
  groupId: string | undefined;
}) => {
  try {
    logger.info('OralExpression', { groupId });
    const session = await getAuthSession();

    if (!session?.user) {
      return <ErrorDisplay error="Session utilisateur non trouvée" />;
    }

    const token = session.user.accessToken || '';
    const response =
      session.user.role === 'dealer'
        ? await getTestsEO(token)
        : await getTestsEOSuperdealer(token, groupId);

    if (!response.success) {
      return <ErrorDisplay error={response.error} />;
    }

    return <ExpressionList data={response.data} />;
  } catch (error) {
    logger.error('Erreur dans OralExpression:', error);
    return (
      <ErrorDisplay error="Erreur inattendue lors du chargement des tests d'expression orale" />
    );
  }
};

export const ExpressionLoader = () => {
  return <Skeleton className="h-[100px] w-full rounded-md" />;
};
