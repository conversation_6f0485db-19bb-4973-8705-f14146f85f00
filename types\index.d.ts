import { User } from 'next-auth';

export const TYPEProduction = {
  Paragraphe: 'Paragraphe',
  Couri<PERSON>: 'Couri<PERSON>',
  Lettre: 'Lettre',
} as const;

type ObjectValues<T> = T[keyof T];
type TypeProduction = ObjectValues<typeof TYPEProduction>;

type Suggestion = {
  text: string;
  isCorrect: boolean;
  _id: string;
};
type Question = {
  numero: number;
  libelle: string;
  consigne: string;
  suggestions: Suggestion[];
};

type Task = {
  _id: string;
  numero: number;
  libelle: string;
  typeProduction: TypeProduction;
  correction?: string;
  consigne: string;
  images: string[] | null;
  minWord: number;
  maxWord: number;
};
type EOTask = {
  _id: string;
  numero: number;
  libelle: string;
  consigne: string;
  correction?: string;
  fichier: string;
  duree: number;
  images: string[];
};
type EEQuestion = {
  _id: string;
  tasks: Task[];
};

type EOQuestion = {
  _id: string;
  tasks: EOTask[];
};

type Serie = {
  _id: string;
  libelle: string;
  questions: Question[];
  eeQuestions: EEQuestion[];
  eoQuestions: EOQuestion[];
};
type Resultset = {
  questionId: number;
  consigneId?: string;
  resId: string;
};
type TestType = 'TCF' | 'TEF';
type TestSet = {
  serieId: string;
  libelle: string;
  CE: Question[] | null;
  CO: Question[] | null;
  EO: EOQuestion[] | null;
  EE: EEQuestion[] | null;
};

type ScoreC = {
  CE: number | null;
  CO: number | null;
};

interface EEPayload {
  textOne: string;
  textTwo: string;
  textThree: string;
}
interface EOPayload {
  taskUrl1: string | null;
  taskUrl2: string | null;
  taskUrl3: string | null;
}
type Resultat = {
  _id: string;
  serie: Serie;
  payload: string;
  user: any;
  resultat: number;
  createdAt: string;
};

type ResultatEE<
  T = Pick<Serie, 'libelle' | '_id' | 'eeQuestions' | 'eoQuestions'>,
> = {
  _id: string;
  serie: T;
  payload: string;
  user: User;
  resultat: {
    task: string;
    note: number;
    comment: string | null;
    _id: string;
    createdAt: string;
  }[];
  isView: boolean;
  status: 'en cours' | 'terminer';
  createdAt: string;
};

type ResultatResponse = {
  _id: string;
  serie: string;
  payload: string;
  user: string;
  resultat: number;
  createdAt: string;
};

type Row = {
  id: string;
  time: string;
  serie: string;
  isEEview: boolean;
  isEOview: boolean;
  CE: string | null;
  CO: string | null;
  EO: string | null;
  EE: string | null;
  MOY: string | null;
};

type FillueilRow = {
  email: string;
  last: string;
  end: string;
};

type Country = {
  name: {
    common: string;
    official: string;
    nativeName?: {
      [languageCode: string]: {
        official: string;
        common: string;
      };
    };
  };
  flags: {
    png: string;
    svg: string;
    alt?: string;
  };
  idd: {
    root: string;
    suffixes?: string[];
  };
  region: string;
  subregion?: string;
  cca3: string;
};

type Partner = {
  _id: string;
  nomPrestataire: string;
  startDate: string;
  endDate: string;
  countClic: number;
  linkTarget: string;
  adsPicture: string;
  localisation: string;
  createdAt: string;
};

type CombinedType = {
  amount: string | number;
  currency: string;
  description: string;
  reference: string;
  phone: string | number;
  paypalEmail?: string;
  channel: string;
  name?: string;
  email?: string;
  lang?: string;
  reason?: string;
  examen?: string;
  offre?: string;
  parrain?: string;
};

type LibelleSerie = {
  libelle: string;
  session: string | undefined;
};

type Profile = {
  remains:
    | {
        remainTCF: Remain | undefined;
        remainTEF: Remain | undefined;
      }
    | undefined;
  _id: string;
  email_profil: string;
  nom_profil: string;
  createdAt: string;
  pin: string;
  userId: string;
};

interface IUser {
  role?: Role;
  _id: string;
  id: string;
  phone: string;
  pays: string;
  email: string;
  role: Role;
  remains:
    | {
        remainTCF: Remain | undefined;
        remainTEF: Remain | undefined;
      }
    | undefined;
  remainsDeals:
    | {
        remainTCF: Remain | undefined;
        remainTEF: Remain | undefined;
      }
    | undefined;
  accessToken: string;
  codePromo: string;
  solde: number | undefined;
  accountIsCheck: boolean | undefined;
  lastConnexion: string;
  createdAt: string;
  type: 'user' | 'profile';
  dealerId?: string;
}

declare module 'html2pdf.js';

interface ExpressionTest {
  _id: string;
  serie: {
    libelle: string;
  };
  user: {
    email: string;
  };
  createdAt: string;
}

export type ExpressionTestResult = ExpressionTest & { type: 'EE' | 'EO' };

export interface SubscriptionPack {
  _id: string;
  title: string;
  subtitle: string;
  description: string;
  code: string;
  promotionDays: number | undefined;
  cameroun: RegionPricing;
  afrique_centrale: RegionPricing;
  afrique_ouest: RegionPricing;
  international: RegionPricing;
  validityDays: number;
  soldeEE: number;
  soldeEO: number;
  profilsNumber: number;
  type: 'subscription';
  targetAudience: string;
  isActive: boolean;
  createdBy: string;
  createdAt: string; // ISO Date string, si tu veux un Date : Date
}

export interface RegionPricing {
  local: number;
  euro: number;
  oldLocal: number;
  oldEuro: number;
}

interface Group {
  _id: string;
  name: string;
  managerName: string;
  location: string;
  owner: string;
  profiles: Profile[];
  createdAt: string;
}
