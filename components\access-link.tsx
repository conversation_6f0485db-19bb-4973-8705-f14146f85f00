'use client';

import { useSession } from 'next-auth/react';
import Link from 'next/link';

function AccessLinks() {
  const { data: session, status } = useSession();
  if (status == 'loading') {
    return <div>Loading...</div>;
  }
  if (status == 'unauthenticated') {
    return (
      <Link
        href={'/dashboard/profiles'}
        className="mx-auto text-[1.025rem] text-purple-400 underline-offset-2"
      >
        Espace professionnel
      </Link>
    );
  }

  if (status == 'authenticated' && session?.user.role == 'admin') {
    return (
      <Link
        href={'/dashboard/administration'}
        className="mx-auto text-[1.025rem] text-purple-400 underline-offset-2"
      >
        Administration
      </Link>
    );
  }
}

export default AccessLinks;
