import { getAuthSession } from '@/lib/auth';
import SERVER_API from '@/lib/axios.server';
import { Group } from '@/types';
import Link from 'next/link';
import React from 'react';

const getGroups = async (userId: string) => {
  try {
    const { data } = await SERVER_API.get<{ groups: Group[] }>(
      `/api/superdealer/groups/owner/${userId}`,
    );
    return { groups: data.groups, error: false };
  } catch (error) {
    return { groups: [], error: true };
  }
};

async function GroupList() {
  const session = await getAuthSession();
  const { groups, error } = await getGroups(session?.user._id || '');
  if (error) return <div>Une erreur est survenue</div>;
  if (!groups.length) return <div>Vous n'avez pas de groupes</div>;
  return (
    <div className="flex flex-wrap items-center justify-center gap-4">
      {groups.map((group) => (
        <GroupCard group={group} key={group._id} />
      ))}
    </div>
  );
}

export default GroupList;

const GroupCard = ({ group }: { group: Group }) => {
  return (
    <Link href={`/dashboard/superdealer-groups/${group._id}`}>
      <div className="flex flex-col items-center gap-2 rounded-lg border p-4">
        <div className="ml-auto rounded-[4px] bg-blue-500 px-1 py-0.5">
          <span className="text-sm text-white">
            {group.profiles.length} profils
          </span>
        </div>
        <h3 className="text-lg font-semibold">{group.name}</h3>
        <p className="text-sm text-muted-foreground">
          {group.managerName} - {group.location}
        </p>
      </div>
    </Link>
  );
};
